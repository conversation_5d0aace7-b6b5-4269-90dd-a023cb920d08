import { PlayMode } from "../definitions/startGame";
import { Currencies } from "@skywind-group/sw-currency-exchange";

/**
 * Converts play mode based on currency properties and merchant configuration.
 * 
 * This function implements automatic mode detection for FUN_BONUS mode:
 * - If currency has funBonus property set to true, returns FUN_BONUS mode
 * - Otherwise returns the original playMode or REAL as default
 * 
 * @param playMode - The requested play mode
 * @param currency - The currency code to check for funBonus property
 * @returns The appropriate PlayMode based on currency properties
 */
export function playModeConverter(
    playMode: PlayMode | undefined,
    currency: string | undefined
): PlayMode {
    // Check if currency has funBonus property
    if (currency) {
        try {
            const currencyObj = Currencies.value(currency.toUpperCase());
            if (currencyObj?.funBonus) {
                return PlayMode.FUN_BONUS;
            }
        } catch (error) {
            // If currency is not found, continue with original logic
            console.warn(`Currency ${currency} not found in currency exchange:`, error);
        }
    }
    
    // Return original playMode or default to REAL
    return playMode || PlayMode.REAL;
}

/**
 * Validates if a currency is eligible for FUN_BONUS mode.
 * 
 * @param currency - The currency code to validate
 * @returns true if currency has funBonus property, false otherwise
 */
export function isFunBonusCurrency(currency: string): boolean {
    try {
        const currencyObj = Currencies.value(currency.toUpperCase());
        return currencyObj?.funBonus === true;
    } catch (error) {
        return false;
    }
}

/**
 * Gets all available fun bonus currencies.
 * 
 * @returns Array of currency codes that have funBonus property set to true
 */
export function getFunBonusCurrencies(): string[] {
    try {
        const allCurrencies = Currencies.all();
        return allCurrencies
            .filter(currency => currency.funBonus === true)
            .map(currency => currency.iso);
    } catch (error) {
        console.warn("Error getting fun bonus currencies:", error);
        return [];
    }
}
