import { GameMode } from "@skywind-group/sw-game-core";
import { GameTokenData } from "./tokens";
import { BNSGameData, GameData } from "./auth";
import { getCurrencyExchange } from "./currencyexchange";
import { GameFlowContextImpl } from "./context/gameContextImpl";
import {
    createForceFinishGameEvent,
    createRoundHistoryEvent,
    GameEventHistory,
    RoundHistory
} from "../history/history";
import { RecoveryType } from "./offlinestorage/offlineCommands";
import { Currencies } from "@skywind-group/sw-currency-exchange";

const BNS_CURRENCY = "BNS";

export class PlayMode {

    public static supportJP(gameMode: GameMode) {
        return gameMode === "real" || gameMode === "fun_bonus" || !gameMode;
    }

    // todo should be removed eventully
    public static getModeByCurrency(currency: string): GameMode {
        return currency === BNS_CURRENCY ? "bns" : "real";
    }

    public static getCurrency(gameTokenData: GameTokenData) {
        if (gameTokenData.playmode === "bns") {
            return BNS_CURRENCY;
        } else if (gameTokenData.playmode === "fun_bonus") {
            // For FUN_BONUS mode, validate that currency has funBonus property
            const currency = gameTokenData.currency;
            try {
                const currencyObj = Currencies.value(currency.toUpperCase());
                if (!currencyObj?.funBonus) {
                    throw new Error(`Currency ${currency} is not enabled for fun bonus mode`);
                }
            } catch (error) {
                throw new Error(`Currency ${currency} validation failed for fun bonus mode: ${error.message}`);
            }
            return currency;
        } else {
            return gameTokenData.currency;
        }
    }

    public static clearContextAndCreateHistoryIfNeeded(context: GameFlowContextImpl, gameData: GameData):
        { roundHistory: RoundHistory, gameEventHistory: GameEventHistory } {
        let roundHistory;
        let gameEventHistory;
        if (gameData.gameTokenData.playmode === "bns") {
            const currentData = gameData as BNSGameData;
            const lastData = context.gameData as BNSGameData;
            if (lastData?.bnsPromotion?.promoId !== currentData?.bnsPromotion?.promoId) {
                if (context?.unfinished) {
                    roundHistory = createRoundHistoryEvent(context);
                    roundHistory.recoveryType = "finalize";
                    roundHistory.finishedAt = new Date();
                    gameEventHistory = createForceFinishGameEvent(context, RecoveryType.FINALIZE);
                }
                context.round = undefined;
                context.roundId = undefined;
                context.roundEnded = true;
                context.gameContext = undefined;
            }
        }
        return { roundHistory, gameEventHistory };
    }

    public static exchange(gameData: GameData, amount: number, targetCurrency: string, baseCurrency?: string): number {
        const playerCurrency = gameData.gameTokenData.currency;
        if (gameData.gameTokenData.playmode === "bns"
            && (baseCurrency === BNS_CURRENCY || targetCurrency === BNS_CURRENCY)) {
            const bnsData = gameData as BNSGameData;
            const exchangeRate = bnsData.bnsPromotion.exchangeRate;
            let result = amount;
            if (baseCurrency === BNS_CURRENCY || !baseCurrency) {
                result = getCurrencyExchange()
                    .exchange(result,
                        BNS_CURRENCY,
                        playerCurrency,
                        { [BNS_CURRENCY]: { [playerCurrency]: exchangeRate } });

            } else {
                result = getCurrencyExchange()
                    .exchange(result, baseCurrency, playerCurrency);
            }

            if (targetCurrency === BNS_CURRENCY) {
                result = getCurrencyExchange()
                    .exchange(result,
                        playerCurrency,
                        BNS_CURRENCY,
                        { [playerCurrency]: { [BNS_CURRENCY]: 1 / exchangeRate } });
            } else {
                result = getCurrencyExchange()
                    .exchange(result, playerCurrency, targetCurrency);
            }

            return result;
        } else if (gameData.gameTokenData.playmode === "play_money") {
            if (Currencies.get(targetCurrency).isSocial) {
                return getCurrencyExchange()
                    .exchange(amount, baseCurrency || playerCurrency, targetCurrency);
            } else {
                return amount;
            }
        } else if (gameData.gameTokenData.playmode === "fun_bonus") {
            // FUN_BONUS mode behaves like REAL mode for currency exchange
            // funBonus currencies use existing exchange logic
            return getCurrencyExchange()
                .exchange(amount, baseCurrency || playerCurrency, targetCurrency);
        } else {
            return getCurrencyExchange()
                .exchange(amount, baseCurrency || playerCurrency, targetCurrency);
        }
    }
}
