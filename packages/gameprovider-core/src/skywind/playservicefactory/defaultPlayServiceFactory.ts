import { PlayServiceFactory } from "./playServiceFactory";
import { PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import {
    BonusCoinsPlayService,
    MerchantPlayService,
    MerchantTransferablePlayService,
    OperatorDetails,
    PlayMoneyMerchantPlayService,
    PlayService,
    PlayServiceImpl
} from "@skywind-group/sw-management-playservice";
import { PlayerWalletImpl } from "@skywind-group/sw-management-wallet";
import { GameProviderErrors } from "../errors";
import { lookupAdapter } from "@skywind-group/sw-management-adapters";
import { OperatorInfo } from "@skywind-group/sw-management-gameprovider";

export class DefaultPlayServiceFactory implements PlayServiceFactory {
    public async create(operatorInfo: OperatorInfo,
                        playMode: PlayMode,
                        transferEnabled?: boolean): Promise<PlayService> {
        const operatorDetails = await this.createDetails(operatorInfo);

        return this.getPlayServiceByToken(operatorDetails, playMode, transferEnabled);
    }

    private async getPlayServiceByToken(operatorDetails: OperatorDetails,
                                        playMode: PlayMode, transferEnabled?: boolean): Promise<PlayService> {
        if (playMode === PlayMode.FUN) {
            return Promise.reject(new GameProviderErrors.OperationForbidden("Request is not supported in fun mode"));
        }
        return this.getPlayService(operatorDetails, playMode, transferEnabled);
    }

    private async getPlayService(operatorDetails: OperatorDetails,
                                 playmode: string,
                                 transferEnabled?: boolean): Promise<PlayService> {
        let playService: PlayService;

        if (operatorDetails.merchant) {
            if (transferEnabled) {
                const walletPerGame = operatorDetails.merchant.info?.params?.walletPerGame;
                playService = new MerchantTransferablePlayService(operatorDetails,
                    walletPerGame ?
                    (data) => PlayerWalletImpl.getWalletPerGameAccount(data.gameCode, data.deviceId) :
                    PlayerWalletImpl.getMainAccountKey);
            } else {
                playService = new MerchantPlayService(operatorDetails);
            }

        } else {
            playService = new PlayServiceImpl(operatorDetails);
        }

        switch (playmode) {
            case PlayMode.BNS:
                playService = new BonusCoinsPlayService(operatorDetails, playService);
                break;
            case PlayMode.PLAY_MONEY:
                playService = new PlayMoneyMerchantPlayService(operatorDetails, playService);
                break;
            case PlayMode.FUN_BONUS:
                // FUN_BONUS uses standard MerchantPlayService (like REAL mode)
                // No special wrapper needed - currency validation is handled elsewhere
                break;
        }

        return playService;
    }

    private async createDetails(operatorInfo: OperatorInfo): Promise<OperatorDetails> {
        return {
            id: operatorInfo.id,
            merchant: operatorInfo.info && {
                info: operatorInfo.info,
                adapter: await lookupAdapter({ ...operatorInfo.typeInfo, info: operatorInfo.info })
            }
        };
    }
}
