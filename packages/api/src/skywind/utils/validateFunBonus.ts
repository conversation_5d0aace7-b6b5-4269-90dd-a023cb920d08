import { BrandEntity } from "../entities/brand";
import { Merchant } from "../entities/merchant";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import * as Errors from "../errors";

/**
 * Validates that a currency is eligible for FUN_BONUS mode.
 * 
 * This function checks if the provided currency has the funBonus property set to true.
 * Unlike PLAY_MONEY mode, FUN_BONUS mode does not require merchant-level configuration
 * as it is controlled entirely at the currency level.
 * 
 * @param brand - The brand entity (not used for funBonus validation but kept for consistency)
 * @param currency - The currency code to validate
 * @param merchant - The merchant entity (not used for funBonus validation but kept for consistency)
 * @returns true if currency is valid for FUN_BONUS mode
 * @throws ValidationError if currency is not found or not enabled for fun bonus mode
 */
export function validateFunBonus(
    brand: BrandEntity, 
    currency: string, 
    merchant?: Merchant
): boolean {
    // Validate that currency exists
    let currencyObj;
    try {
        currencyObj = Currencies.value(currency.toUpperCase());
    } catch (error) {
        throw new Errors.CurrencyNotFunBonusEnabledError(currency);
    }
    
    if (!currencyObj) {
        throw new Errors.FunBonusCurrencyNotFoundError(currency);
    }
    
    // Validate that currency has funBonus property set to true
    if (!currencyObj.funBonus) {
        throw new Errors.CurrencyNotFunBonusEnabledError(currency);
    }
    
    return true;
}

/**
 * Checks if a currency is eligible for FUN_BONUS mode without throwing errors.
 * 
 * @param currency - The currency code to check
 * @returns true if currency has funBonus property, false otherwise
 */
export function isCurrencyFunBonusEnabled(currency: string): boolean {
    try {
        const currencyObj = Currencies.value(currency.toUpperCase());
        return currencyObj?.funBonus === true;
    } catch (error) {
        return false;
    }
}

/**
 * Gets validation error message for FUN_BONUS currency issues.
 * 
 * @param currency - The currency code that failed validation
 * @returns Descriptive error message
 */
export function getFunBonusValidationError(currency: string): string {
    try {
        const currencyObj = Currencies.value(currency.toUpperCase());
        if (!currencyObj) {
            return `Currency ${currency} not found or not configured for fun bonus mode.`;
        }
        if (!currencyObj.funBonus) {
            return `Currency ${currency} is not enabled for fun bonus mode. Only currencies with funBonus property can be used in fun bonus mode.`;
        }
        return `Currency ${currency} validation passed.`;
    } catch (error) {
        return `Currency ${currency} not found or not configured for fun bonus mode.`;
    }
}
